<template>
	<view class="template-basic-info">
		<!-- 模版名称区域 -->
		<view class="template-name-section">
			<input 
				:value="templateName" 
				@input="onTemplateNameInput"
				class="template-name-input" 
				placeholder="未命名"
			/>
		</view>
		
		<!-- 模版类型 -->
		<view class="template-type-section">
			<text class="template-type-label">模板类型</text>
			<picker 
				:value="templateTypeIndex" 
				:range="templateTypeList" 
				@change="onTemplateTypeChange"
				class="template-type-picker"
			>
				<view class="picker-view">
					<text class="picker-text">{{templateTypeList[templateTypeIndex] || '请选择'}}</text>
					<text class="picker-arrow">▼</text>
				</view>
			</picker>
		</view>
	</view>
</template>

<script>
export default {
	name: 'TemplateBasicInfo',
	props: {
		templateName: {
			type: String,
			default: ''
		},
		templateTypeIndex: {
			type: Number,
			default: 0
		},
		templateTypeList: {
			type: Array,
			default: () => ['三色桌牌', '四色桌牌', '七色桌牌']
		}
	},
	methods: {
		onTemplateNameInput(e) {
			this.$emit('update:template-name', e.detail.value);
		},
		
		onTemplateTypeChange(e) {
			const newIndex = e.detail.value;
			this.$emit('update:template-type-index', newIndex);
			this.$emit('template-type-change', e);
		}
	}
}
</script>

<style scoped>
.template-basic-info {
	margin-bottom: 20px;
}

/* 模版名称区域 */
.template-name-section {
	margin-bottom: 20px;
	padding: 0 5px;
}

.template-name-input {
	height: 40px;
	border: 1px solid #d0d7de;
	border-radius: 6px;
	padding: 0 12px;
	font-size: 14px;
	background-color: #f6f8fa;
	width: 100%;
	box-sizing: border-box;
}

/* 模板类型区域 */
.template-type-section {
	margin-bottom: 20px;
	padding: 10px 5px;
	display: flex;
	align-items: center;
	background-color: #f6f8fa;
	border-radius: 8px;
}

.template-type-label {
	width: 80px;
	font-size: 14px;
	color: #24292f;
	flex-shrink: 0;
	font-weight: 500;
}

.template-type-picker {
	flex: 1;
	margin-left: 10px;
}

.picker-view {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 35px;
	line-height: 35px;
	padding: 0 12px;
	border: 1px solid #d0d7de;
	border-radius: 6px;
	background-color: #ffffff;
	font-size: 14px;
	color: #656d76;
}

.picker-arrow {
	font-size: 12px;
}
</style>
