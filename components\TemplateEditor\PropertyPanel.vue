<template>
	<view class="property-panel">
		<!-- 属性设置标题 -->
		<view class="property-title">
			<text>属性设置</text>
			<text v-if="!selectedElement" class="property-subtitle">（修改所有文本元素）</text>
			<text v-else class="property-subtitle">（编辑当前元素）</text>
		</view>
		
		<!-- 文字相关属性 -->
		<view class="property-group">
			<!-- 文字字体 -->
			<view class="property-item">
				<text class="property-label">文字字体</text>
				<picker 
					:value="fontFamilyIndex" 
					:range="fontFamilyList" 
					@change="$emit('font-family-change', $event)"
					class="property-picker"
				>
					<view class="picker-view">
						<text class="picker-text">{{fontFamilyList[fontFamilyIndex] || '请选择'}}</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<!-- 文字颜色 -->
			<view class="property-item">
				<text class="property-label">文字颜色</text>
				<view class="color-picker" @click="$emit('text-color-picker')">
					<view class="color-preview" :style="{backgroundColor: textColor}"></view>
					<text class="color-name">{{colorNameMap[textColor]}}</text>
					<text class="picker-arrow">▼</text>
				</view>
			</view>

			<!-- 文字字形 -->
			<view class="property-item">
				<text class="property-label">文字字形</text>
				<view class="text-style-controls">
					<view 
						class="style-btn" 
						:class="{active: isBold}"
						@click="$emit('toggle-bold')"
					>
						<text class="style-icon">B</text>
					</view>
					<view 
						class="style-btn" 
						:class="{active: isItalic}"
						@click="$emit('toggle-italic')"
					>
						<text class="style-icon italic">I</text>
					</view>
					<view 
						class="style-btn" 
						:class="{active: isUnderline}"
						@click="$emit('toggle-underline')"
					>
						<text class="style-icon underline">U</text>
					</view>
					<view 
						class="style-btn" 
						:class="{active: isStrikethrough}"
						@click="$emit('toggle-strikethrough')"
					>
						<text class="style-icon strikethrough">S</text>
					</view>
				</view>
			</view>
			
			<!-- 文字高度拉伸 -->
			<view class="property-item">
				<text class="property-label">高度拉伸</text>
				<view class="slider-control">
					<view class="slider-btn" @click="$emit('height-scale-decrease')">-</view>
					<slider 
						:value="heightScale" 
						:min="0.5" 
						:max="2.0" 
						:step="0.1"
						@change="$emit('height-scale-change', $event)"
						class="property-slider"
						show-value
					/>
					<view class="slider-btn" @click="$emit('height-scale-increase')">+</view>
				</view>
				<text class="slider-value">{{heightScale.toFixed(1)}}</text>
			</view>
			
			<!-- 文字字号 -->
			<view class="property-item">
				<text class="property-label">文字字号</text>
				<view class="slider-control">
					<view class="slider-btn" @click="$emit('font-size-decrease')">-</view>
					<slider 
						:value="fontSize" 
						:min="8" 
						:max="80" 
						:step="1"
						@change="$emit('font-size-change', $event)"
						class="property-slider"
						show-value
					/>
					<view class="slider-btn" @click="$emit('font-size-increase')">+</view>
				</view>
				<text class="slider-value">{{fontSize}}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PropertyPanel',
	props: {
		selectedElement: {
			type: Object,
			default: null
		},
		fontFamilyIndex: {
			type: Number,
			default: 0
		},
		fontFamilyList: {
			type: Array,
			default: () => ['微软雅黑', '宋体', '黑体', 'Arial', 'Times New Roman']
		},
		textColor: {
			type: String,
			default: '#000000'
		},
		colorNameMap: {
			type: Object,
			default: () => ({})
		},
		isBold: {
			type: Boolean,
			default: false
		},
		isItalic: {
			type: Boolean,
			default: false
		},
		isUnderline: {
			type: Boolean,
			default: false
		},
		isStrikethrough: {
			type: Boolean,
			default: false
		},
		heightScale: {
			type: Number,
			default: 1.0
		},
		fontSize: {
			type: Number,
			default: 16
		}
	},
	emits: [
		'font-family-change',
		'text-color-picker',
		'toggle-bold',
		'toggle-italic',
		'toggle-underline',
		'toggle-strikethrough',
		'height-scale-change',
		'height-scale-increase',
		'height-scale-decrease',
		'font-size-change',
		'font-size-increase',
		'font-size-decrease'
	]
}
</script>

<style scoped>
.property-panel {
	margin-bottom: 30px;
}

/* 属性设置标题 */
.property-title {
	font-size: 18px;
	font-weight: bold;
	text-align: center;
	margin-bottom: 20px;
	color: #24292f;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.property-subtitle {
	font-size: 14px;
	font-weight: normal;
	color: #57606a;
	margin-top: 5px;
}

/* 属性组 */
.property-group {
	margin-bottom: 30px;
	padding-bottom: 20px;
	overflow: visible;
}

/* 属性项 */
.property-item {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
	padding: 10px 5px;
	flex-wrap: wrap;
}

.property-label {
	width: 80px;
	font-size: 14px;
	color: #24292f;
	flex-shrink: 0;
}

/* 选择器样式 */
.property-picker {
	flex: 1;
	margin-left: 10px;
}

.picker-view {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 35px;
	line-height: 35px;
	padding: 0 12px;
	border: 1px solid #d0d7de;
	border-radius: 6px;
	background-color: #f6f8fa;
	font-size: 14px;
	color: #656d76;
}

.picker-arrow {
	font-size: 12px;
}

/* 颜色选择器 */
.color-picker {
	flex: 1;
	margin-left: 10px;
	display: flex;
	align-items: center;
	cursor: pointer;
}

.color-preview {
	width: 35px;
	height: 35px;
	border: 1px solid #d0d7de;
	border-radius: 6px;
	margin-right: 10px;
}

.color-name {
	flex: 1;
	font-size: 14px;
	color: #24292f;
}

/* 文字样式控制 */
.text-style-controls {
	display: flex;
	align-items: center;
	flex: 1;
	margin-left: 10px;
	gap: 8px;
}

.style-btn {
	width: 30px;
	height: 30px;
	border: 1px solid #d0d7de;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f6f8fa;
	cursor: pointer;
}

.style-btn.active {
	background-color: #0969da;
	color: white;
	border-color: #0969da;
}

.style-icon {
	font-size: 14px;
	font-weight: bold;
}

.style-icon.italic {
	font-style: italic;
}

.style-icon.underline {
	text-decoration: underline;
}

.style-icon.strikethrough {
	text-decoration: line-through;
}

/* 滑块控制 */
.slider-control {
	display: flex;
	align-items: center;
	flex: 1;
	margin-left: 10px;
	gap: 8px;
	min-height: 36px;
}

.slider-btn {
	width: 30px;
	height: 30px;
	border: 1px solid #d0d7de;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f6f8fa;
	cursor: pointer;
	z-index: 2;
}

.slider-value {
	width: 40px;
	text-align: center;
	font-size: 14px;
	margin-left: auto;
	margin-right: 10px;
}

.property-slider {
	flex: 1;
	margin: 0 5px;
	z-index: 1;
}
</style>
