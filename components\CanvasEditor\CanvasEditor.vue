<template>
	<view class="canvas-container" id="canvasContainer" 
		@touchstart="onContainerTouchStart"
		@touchmove="onContainerTouchMove"
		@touchend="onContainerTouchEnd">
		<canvas 
			canvas-id="editCanvas" 
			id="editCanvas"
			class="edit-canvas" 
			:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"
		></canvas>
	</view>
</template>

<script>
export default {
	name: 'CanvasEditor',
	props: {
		canvasWidth: {
			type: Number,
			default: 300
		},
		canvasHeight: {
			type: Number,
			default: 180
		},
		canvasElements: {
			type: Array,
			default: () => []
		},
		selectedElement: {
			type: Object,
			default: null
		},
		backgroundImage: {
			type: String,
			default: ''
		},
		backgroundColor: {
			type: String,
			default: '#FFFFFF'
		}
	},
	data() {
		return {
			canvasContext: null,
			canvasRect: null,
			touchStartX: 0,
			touchStartY: 0,
			isDragging: false,
			isScaling: false,
			lastTouches: null,
			initialDistance: 0,
			lastScaleFactor: 1,
			editIconPosition: null,
			deleteIconPosition: null,
			rotateIconPosition: null,
			backgroundImageInfo: null,
			// 性能优化相关
			drawTimer: null,
			lastDrawTime: 0,
			textMetricsCache: new Map(),
			isDrawing: false
		}
	},
	mounted() {
		this.initCanvas();
	},
	watch: {
		canvasElements: {
			handler() {
				this.debouncedDraw();
			},
			deep: true
		},
		selectedElement() {
			this.debouncedDraw();
		},
		backgroundImage() {
			this.backgroundImageInfo = null; // 清除缓存
			this.debouncedDraw();
		},
		backgroundColor() {
			this.debouncedDraw();
		}
	},
	methods: {
		// 初始化Canvas
		initCanvas() {
			setTimeout(() => {
				this.canvasContext = uni.createCanvasContext('editCanvas', this);
				this.updateCanvasRect();
				this.drawCanvas();
			}, 200);
		},

		// 获取Canvas容器的位置信息
		updateCanvasRect() {
			const query = uni.createSelectorQuery().in(this);
			query.select('#canvasContainer').boundingClientRect(data => {
				if (data) {
					this.canvasRect = data;
				}
			}).exec();
		},

		// 防抖绘制
		debouncedDraw() {
			if (this.drawTimer) {
				clearTimeout(this.drawTimer);
			}

			this.drawTimer = setTimeout(() => {
				this.drawCanvas();
			}, 16); // 约60fps
		},

		// 绘制Canvas
		drawCanvas() {
			if (!this.canvasContext || this.isDrawing) {
				return;
			}

			// 防止重复绘制
			const now = Date.now();
			if (now - this.lastDrawTime < 16) { // 限制最高60fps
				return;
			}

			this.isDrawing = true;
			this.lastDrawTime = now;

			const ctx = this.canvasContext;
			ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

			// 绘制背景
			this.drawBackground(ctx);

			// 绘制所有元素
			this.canvasElements.forEach(element => {
				this.drawElement(element);
			});

			// 绘制选中状态
			if (this.selectedElement) {
				this.drawSelectionHandles(this.selectedElement);
			}

			ctx.draw(false, () => {
				this.isDrawing = false;
			});
		},
		
		// 绘制背景
		drawBackground(ctx) {
			if (this.backgroundImage) {
				if (this.backgroundImageInfo) {
					this.drawBackgroundWithRatio(ctx, this.backgroundImage, this.backgroundImageInfo);
				} else {
					uni.getImageInfo({
						src: this.backgroundImage,
						success: (imageInfo) => {
							this.backgroundImageInfo = imageInfo;
							this.drawBackgroundWithRatio(ctx, this.backgroundImage, imageInfo);
							this.drawCanvas();
						},
						fail: () => {
							ctx.setFillStyle(this.backgroundColor);
							ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
						}
					});
					return;
				}
			} else {
				ctx.setFillStyle(this.backgroundColor);
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
			}
		},
		
		// 按比例绘制背景图片
		drawBackgroundWithRatio(ctx, imagePath, imageInfo) {
			const imageRatio = imageInfo.width / imageInfo.height;
			const canvasRatio = this.canvasWidth / this.canvasHeight;
			
			let drawWidth, drawHeight, offsetX = 0, offsetY = 0;
			
			if (imageRatio > canvasRatio) {
				drawHeight = this.canvasHeight;
				drawWidth = drawHeight * imageRatio;
				offsetX = (this.canvasWidth - drawWidth) / 2;
			} else {
				drawWidth = this.canvasWidth;
				drawHeight = drawWidth / imageRatio;
				offsetY = (this.canvasHeight - drawHeight) / 2;
			}
			
			ctx.setFillStyle(this.backgroundColor);
			ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
			ctx.drawImage(imagePath, offsetX, offsetY, drawWidth, drawHeight);
		},
		
		// 绘制单个元素
		drawElement(element) {
			const ctx = this.canvasContext;
			ctx.save();
			
			try {
				if (element.type === 'text') {
					this.drawTextElement(ctx, element);
				} else if (element.type === 'image') {
					this.drawImageElement(ctx, element);
				}
			} catch (e) {
				console.error("绘制元素错误:", e);
			}
			
			ctx.restore();
		},
		
		// 绘制文本元素
		drawTextElement(ctx, element) {
			ctx.setFillStyle(element.color || '#000000');
			
			const fontStyle = element.isItalic ? 'italic' : 'normal';
			const fontWeight = element.isBold ? 'bold' : 'normal';
			const fontSize = element.fontSize || 16;
			const fontFamily = element.fontFamily || 'sans-serif';
			ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;
			
			ctx.setTextAlign(element.textAlign || 'left');
			
			// 处理高度拉伸
			if (element.heightScale && element.heightScale !== 1) {
				ctx.scale(1, element.heightScale);
				const scaledY = element.y / element.heightScale;
				ctx.fillText(element.text, element.x, scaledY);
			} else {
				ctx.fillText(element.text, element.x, element.y);
			}
			
			// 绘制下划线或删除线
			if (element.isUnderline || element.isStrikethrough) {
				this.drawTextDecorations(ctx, element);
			}
		},
		
		// 绘制图片元素
		drawImageElement(ctx, element) {
			let x = Math.max(0, Math.min(element.x, this.canvasWidth - element.width));
			let y = Math.max(0, Math.min(element.y, this.canvasHeight - element.height));
			
			if (x !== element.x || y !== element.y) {
				element.x = x;
				element.y = y;
			}
			
			if (element.rotation && element.rotation !== 0) {
				const centerX = x + element.width / 2;
				const centerY = y + element.height / 2;
				ctx.translate(centerX, centerY);
				ctx.rotate(element.rotation * Math.PI / 180);
				ctx.drawImage(element.src, -element.width / 2, -element.height / 2, element.width, element.height);
			} else {
				ctx.drawImage(element.src, x, y, element.width, element.height);
			}
		},
		
		// 绘制文本装饰线
		drawTextDecorations(ctx, element) {
			const metrics = ctx.measureText(element.text);
			const textWidth = metrics.width;
			let lineY = element.y;
			
			if (element.heightScale && element.heightScale !== 1) {
				lineY = element.y / element.heightScale;
			}
			
			if (element.isUnderline) {
				lineY = lineY + 2;
			}
			if (element.isStrikethrough) {
				lineY = lineY - (element.fontSize || 16) / 3;
			}
			
			let lineStartX = element.x;
			if (element.textAlign === 'center') {
				lineStartX = element.x - textWidth / 2;
			} else if (element.textAlign === 'right') {
				lineStartX = element.x - textWidth;
			}
			
			ctx.beginPath();
			ctx.setStrokeStyle(element.color || '#000000');
			ctx.setLineWidth(1);
			ctx.moveTo(lineStartX, lineY);
			ctx.lineTo(lineStartX + textWidth, lineY);
			ctx.stroke();
		},
		
		// 绘制选中状态
		drawSelectionHandles(element) {
			const ctx = this.canvasContext;
			const box = this.getElementBoundingBox(element);
			
			ctx.save();
			ctx.setStrokeStyle('#0969da');
			ctx.setLineWidth(2);
			
			if (element.type === 'image' && element.rotation && element.rotation !== 0) {
				const centerX = element.x + element.width / 2;
				const centerY = element.y + element.height / 2;
				
				ctx.translate(centerX, centerY);
				ctx.rotate(element.rotation * Math.PI / 180);
				
				const halfWidth = element.width / 2;
				const halfHeight = element.height / 2;
				ctx.strokeRect(-halfWidth, -halfHeight, element.width, element.height);
				
				ctx.rotate(-element.rotation * Math.PI / 180);
				ctx.translate(-centerX, -centerY);
			} else {
				ctx.strokeRect(box.x, box.y, box.width, box.height);
			}
			
			// 绘制操作图标
			this.drawOperationIcons(ctx, element, box);
			
			ctx.restore();
		},
		
		// 绘制操作图标
		drawOperationIcons(ctx, element, box) {
			const iconRadius = 18;
			
			if (element.type === 'image') {
				// 图片元素显示删除和旋转图标
				const deleteIconX = box.x + box.width + iconRadius;
				const deleteIconY = box.y - iconRadius;
				const rotateIconX = box.x - iconRadius;
				const rotateIconY = box.y - iconRadius;
				
				this.deleteIconPosition = { x: deleteIconX, y: deleteIconY, radius: iconRadius * 1.5 };
				this.rotateIconPosition = { x: rotateIconX, y: rotateIconY, radius: iconRadius * 1.5 };
				this.editIconPosition = null;
				
				// 绘制删除图标
				ctx.beginPath();
				ctx.setFillStyle('#f44336');
				ctx.arc(deleteIconX, deleteIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制旋转图标
				ctx.beginPath();
				ctx.setFillStyle('#0969da');
				ctx.arc(rotateIconX, rotateIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制图标内容
				this.drawDeleteIcon(ctx, deleteIconX, deleteIconY);
				this.drawRotateIcon(ctx, rotateIconX, rotateIconY, iconRadius);
				
			} else {
				// 文本元素显示编辑和删除图标
				const editIconX = box.x - iconRadius;
				const editIconY = box.y - iconRadius;
				const deleteIconX = box.x + box.width + iconRadius;
				const deleteIconY = box.y - iconRadius;
				
				this.editIconPosition = { x: editIconX, y: editIconY, radius: iconRadius * 1.5 };
				this.deleteIconPosition = { x: deleteIconX, y: deleteIconY, radius: iconRadius * 1.5 };
				this.rotateIconPosition = null;
				
				// 绘制编辑图标
				ctx.beginPath();
				ctx.setFillStyle('#0969da');
				ctx.arc(editIconX, editIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制删除图标
				ctx.beginPath();
				ctx.setFillStyle('#f44336');
				ctx.arc(deleteIconX, deleteIconY, iconRadius, 0, 2 * Math.PI);
				ctx.fill();
				
				// 绘制图标内容
				this.drawEditIcon(ctx, editIconX, editIconY);
				this.drawDeleteIcon(ctx, deleteIconX, deleteIconY);
			}
		},

		// 绘制删除图标
		drawDeleteIcon(ctx, x, y) {
			ctx.beginPath();
			ctx.setStrokeStyle('#ffffff');
			ctx.setLineWidth(2.5);
			ctx.moveTo(x - 7, y - 7);
			ctx.lineTo(x + 7, y + 7);
			ctx.moveTo(x + 7, y - 7);
			ctx.lineTo(x - 7, y + 7);
			ctx.stroke();
		},

		// 绘制编辑图标
		drawEditIcon(ctx, x, y) {
			ctx.setFillStyle('#ffffff');
			ctx.setStrokeStyle('#ffffff');
			ctx.setLineWidth(2);
			ctx.beginPath();
			ctx.moveTo(x - 7, y + 7);
			ctx.lineTo(x + 5, y - 5);
			ctx.lineTo(x + 7, y - 3);
			ctx.lineTo(x - 5, y + 9);
			ctx.closePath();
			ctx.fill();
		},

		// 绘制旋转图标
		drawRotateIcon(ctx, x, y, radius) {
			ctx.beginPath();
			ctx.setStrokeStyle('#ffffff');
			ctx.setLineWidth(2);
			ctx.arc(x, y, radius * 0.6, 0.3 * Math.PI, 2.2 * Math.PI);
			ctx.stroke();

			ctx.beginPath();
			ctx.moveTo(x + 6, y - 6);
			ctx.lineTo(x, y - 10);
			ctx.lineTo(x - 4, y - 6);
			ctx.setFillStyle('#ffffff');
			ctx.fill();
		},

		// 获取元素边界框
		getElementBoundingBox(element) {
			if (element.type === 'image') {
				return {
					x: element.x,
					y: element.y,
					width: element.width,
					height: element.height
				};
			} else if (element.type === 'text') {
				const ctx = this.canvasContext;
				const fontStyle = element.isItalic ? 'italic' : 'normal';
				const fontWeight = element.isBold ? 'bold' : 'normal';
				const fontSize = element.fontSize || 16;
				const fontFamily = element.fontFamily || 'sans-serif';

				ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;

				let width = 0;
				try {
					const metrics = ctx.measureText(element.text);
					width = metrics.width;
				} catch (e) {
					width = element.text.length * fontSize * 0.6;
				}

				let x = element.x;
				if (element.textAlign === 'center') {
					x = element.x - width / 2;
				} else if (element.textAlign === 'right') {
					x = element.x - width;
				}

				return {
					x: x,
					y: element.y - fontSize,
					width: width,
					height: fontSize * 1.5
				};
			}

			return {
				x: element.x,
				y: element.y,
				width: 10,
				height: 10
			};
		},

		// 触摸事件处理
		onContainerTouchStart(e) {
			e.preventDefault && e.preventDefault();

			if (e.touches.length === 2 && this.selectedElement && this.selectedElement.type === 'image') {
				this.isScaling = true;
				this.isDragging = false;

				const touch1 = e.touches[0];
				const touch2 = e.touches[1];
				const dx = touch1.clientX - touch2.clientX;
				const dy = touch1.clientY - touch2.clientY;
				this.initialDistance = Math.sqrt(dx * dx + dy * dy);

				this.lastTouches = [...e.touches];
				return;
			}

			const touch = e.touches[0];

			if (!this.canvasRect) {
				this.updateCanvasRect();
				return;
			}

			const canvasX = touch.clientX - this.canvasRect.left;
			const canvasY = touch.clientY - this.canvasRect.top;

			this.touchStartX = canvasX;
			this.touchStartY = canvasY;

			// 检查是否点击了操作图标
			if (this.selectedElement) {
				if (this.deleteIconPosition && this.isPointInCircle(canvasX, canvasY, this.deleteIconPosition)) {
					setTimeout(() => {
						this.$emit('element-deleted', this.selectedElement);
					}, 100);
					return;
				}

				if (this.editIconPosition && this.isPointInCircle(canvasX, canvasY, this.editIconPosition)) {
					setTimeout(() => {
						this.$emit('element-edit', this.selectedElement);
					}, 100);
					return;
				}

				if (this.rotateIconPosition && this.isPointInCircle(canvasX, canvasY, this.rotateIconPosition)) {
					setTimeout(() => {
						this.rotateSelectedElement();
					}, 100);
					return;
				}
			}

			// 选择元素
			const selectedElement = this.selectElementAt(canvasX, canvasY);
			this.$emit('element-selected', selectedElement);

			if (selectedElement) {
				this.isDragging = true;
			}
		},

		onContainerTouchMove(e) {
			if (e.touches.length === 2 && this.isScaling && this.selectedElement && this.selectedElement.type === 'image') {
				const touch1 = e.touches[0];
				const touch2 = e.touches[1];
				const dx = touch1.clientX - touch2.clientX;
				const dy = touch1.clientY - touch2.clientY;
				const currentDistance = Math.sqrt(dx * dx + dy * dy);

				const scaleFactor = currentDistance / this.initialDistance;

				if (scaleFactor > 0.1) {
					const originalWidth = this.selectedElement.width / (this.lastScaleFactor || 1);
					const originalHeight = this.selectedElement.height / (this.lastScaleFactor || 1);

					let newWidth = originalWidth * scaleFactor;
					let newHeight = originalHeight * scaleFactor;

					const minSize = 30;
					if (newWidth < minSize) {
						newWidth = minSize;
						newHeight = (minSize / originalWidth) * originalHeight;
					}
					if (newHeight < minSize) {
						newHeight = minSize;
						newWidth = (minSize / originalHeight) * originalWidth;
					}

					const maxWidth = this.canvasWidth * 0.95;
					const maxHeight = this.canvasHeight * 0.95;
					if (newWidth > maxWidth) {
						newWidth = maxWidth;
						newHeight = (maxWidth / originalWidth) * originalHeight;
					}
					if (newHeight > maxHeight) {
						newHeight = maxHeight;
						newWidth = (maxHeight / originalHeight) * originalWidth;
					}

					this.selectedElement.width = newWidth;
					this.selectedElement.height = newHeight;
					this.lastScaleFactor = scaleFactor;

					let newX = this.selectedElement.x;
					let newY = this.selectedElement.y;

					if (newX < 0) newX = 0;
					if (newY < 0) newY = 0;
					if (newX + newWidth > this.canvasWidth) newX = this.canvasWidth - newWidth;
					if (newY + newHeight > this.canvasHeight) newY = this.canvasHeight - newHeight;

					this.selectedElement.x = newX;
					this.selectedElement.y = newY;

					this.$emit('element-updated', this.selectedElement);
					this.drawCanvas();
				}

				this.lastTouches = [...e.touches];
				return;
			}

			if (!this.isDragging || !this.selectedElement || !this.canvasRect) return;

			e.preventDefault && e.preventDefault();

			const touch = e.touches[0];
			const canvasX = touch.clientX - this.canvasRect.left;
			const canvasY = touch.clientY - this.canvasRect.top;

			const deltaX = canvasX - this.touchStartX;
			const deltaY = canvasY - this.touchStartY;

			let newX = this.selectedElement.x + deltaX;
			let newY = this.selectedElement.y + deltaY;

			// 边界检查
			const box = this.getElementBoundingBox(this.selectedElement);

			if (newX < 0) newX = 0;
			if (this.selectedElement.type === 'text') {
				if (newY < box.height) newY = box.height;
			} else {
				if (newY < 0) newY = 0;
			}

			if (newX + box.width > this.canvasWidth) {
				newX = this.canvasWidth - box.width;
			}
			if (newY + box.height > this.canvasHeight) {
				newY = this.canvasHeight - box.height;
			}

			this.selectedElement.x = newX;
			this.selectedElement.y = newY;

			this.touchStartX = canvasX;
			this.touchStartY = canvasY;

			this.$emit('element-updated', this.selectedElement);
			this.drawCanvas();
		},

		onContainerTouchEnd() {
			this.isDragging = false;
			this.isScaling = false;
			this.lastScaleFactor = 1;
			this.lastTouches = null;
		},

		// 判断点是否在圆内
		isPointInCircle(x, y, circle) {
			const dx = x - circle.x;
			const dy = y - circle.y;
			const distance = Math.sqrt(dx * dx + dy * dy);
			return distance <= circle.radius;
		},

		// 旋转选中的元素
		rotateSelectedElement() {
			if (!this.selectedElement || this.selectedElement.type !== 'image') return;

			if (this.selectedElement.rotation === undefined) {
				this.selectedElement.rotation = 0;
			}

			this.selectedElement.rotation = (this.selectedElement.rotation + 45) % 360;

			this.$emit('element-updated', this.selectedElement);
			this.drawCanvas();

			uni.showToast({
				title: `已旋转至${this.selectedElement.rotation}°`,
				icon: 'none',
				duration: 1500
			});
		},

		// 选择元素
		selectElementAt(x, y) {
			for (let i = this.canvasElements.length - 1; i >= 0; i--) {
				const element = this.canvasElements[i];
				const box = this.getElementBoundingBox(element);

				if (x >= box.x && x <= box.x + box.width && y >= box.y && y <= box.y + box.height) {
					return element;
				}
			}
			return null;
		},

		// 公共方法
		exportCanvas() {
			return new Promise((resolve, reject) => {
				uni.canvasToTempFilePath({
					canvasId: 'editCanvas',
					success: resolve,
					fail: reject
				}, this);
			});
		}
	}
}
</script>

<style scoped>
.canvas-container {
	padding: 10px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #ffffff;
	flex: 0 0 33.33vh;
}

.edit-canvas {
	background-color: #f5f5f5;
	border: 1px solid #e0e0e0;
}
</style>
