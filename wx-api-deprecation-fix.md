# 微信小程序 API 废弃警告修复

## 问题描述

微信小程序中 `wx.getSystemInfoSync` API 已被废弃，需要替换为新的 API：
- `wx.getDeviceInfo()` - 获取设备信息
- `wx.getWindowInfo()` - 获取窗口信息  
- `wx.getAppBaseInfo()` - 获取应用基础信息
- `wx.getSystemSetting()` - 获取系统设置
- `wx.getAppAuthorizeSetting()` - 获取应用授权设置

## 修复方案

采用条件编译的方式，在微信小程序平台使用新 API，其他平台继续使用旧 API，并添加错误处理以保证向后兼容。

## 修复的文件

### 1. uni_modules/uview-ui/libs/function/index.js

**修复内容：**
- `os()` 函数：使用 `uni.getDeviceInfo().platform` 替代 `uni.getSystemInfoSync().platform`
- `sys()` 函数：组合使用新 API 返回兼容的系统信息对象

**修复方式：**
```javascript
// #ifdef MP-WEIXIN
try {
  return uni.getDeviceInfo().platform.toLowerCase()
} catch (e) {
  // 兼容旧版本
  return uni.getSystemInfoSync().platform.toLowerCase()
}
// #endif
// #ifndef MP-WEIXIN
return uni.getSystemInfoSync().platform.toLowerCase()
// #endif
```

### 2. pages/home/<USER>

**修复内容：**
- `getSystemInfo()` 方法：使用 `uni.getWindowInfo()` 获取状态栏高度

### 3. pages/subPackage/template/edit_new.vue

**修复内容：**
- `initCanvas()` 方法：使用 `uni.getWindowInfo()` 获取窗口信息
- `resizeCanvas()` 方法：使用 `uni.getWindowInfo()` 获取窗口信息

### 4. uni_modules/uview-ui/components/u-no-network/u-no-network.vue

**修复内容：**
- `mounted()` 生命周期：使用 `uni.getDeviceInfo().platform` 判断平台

### 5. uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue

**修复内容：**
- `created()` 生命周期：组合使用新 API 获取系统信息

### 6. uni_modules/uni-fab/components/uni-fab/uni-fab.vue

**修复内容：**
- 全局变量 `platform`：使用 `uni.getDeviceInfo().platform` 获取平台信息

## 修复特点

1. **向后兼容**：使用 try-catch 确保在不支持新 API 的环境中回退到旧 API
2. **条件编译**：只在微信小程序平台使用新 API，其他平台保持不变
3. **数据结构兼容**：新 API 返回的数据结构与旧 API 保持一致
4. **错误处理**：添加适当的错误处理和警告日志

## 测试建议

1. 在微信开发者工具中测试修复后的功能
2. 确保状态栏高度、Canvas 尺寸等功能正常
3. 测试在不同设备和微信版本上的兼容性
4. 验证其他平台（如 H5、App）功能未受影响

## 注意事项

- 修复仅针对微信小程序平台，其他平台继续使用原有 API
- 保持了完整的向后兼容性
- 所有修改都添加了错误处理机制
- 建议在生产环境部署前进行充分测试
