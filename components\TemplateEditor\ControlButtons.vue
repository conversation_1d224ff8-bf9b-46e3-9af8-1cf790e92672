<template>
	<view class="control-buttons">
		<!-- 对象插入 -->
		<view class="control-group">
			<text class="group-title">对象插入</text>
			<view class="button-row">
				<view class="control-btn" @click="$emit('insert-template-text')">模版文字</view>
				<view class="control-btn" @click="$emit('insert-fixed-text')">固定文字</view>
				<view class="control-btn" @click="$emit('insert-image')">图片</view>
			</view>
		</view>
		
		<!-- 背景选择 -->
		<view class="control-group">
			<text class="group-title">背景选择</text>
			<view class="button-row">
				<view class="control-btn" @click="$emit('select-background')">选择背景</view>
				<view class="control-btn" @click="$emit('set-solid-background')">纯色背景</view>
				<view class="control-btn" @click="$emit('clear-background')">清除背景</view>
			</view>
		</view>
		
		<!-- 批量操作 -->
		<view class="control-group">
			<text class="group-title">批量操作</text>
			<view class="button-row">
				<view class="control-btn" @click="$emit('center-all-elements')">水平居中</view>
				<view class="control-btn" @click="$emit('auto-adjust-spacing')">自适应间距</view>
				<view class="control-btn special-btn" @click="$emit('beautify-all-elements')">一键美化</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ControlButtons',
	emits: [
		'insert-template-text',
		'insert-fixed-text', 
		'insert-image',
		'select-background',
		'set-solid-background',
		'clear-background',
		'center-all-elements',
		'auto-adjust-spacing',
		'beautify-all-elements'
	]
}
</script>

<style scoped>
.control-buttons {
	margin-top: 30px;
	padding-top: 20px;
	border-top: 1px solid #e0e0e0;
}

.control-group {
	margin-bottom: 20px;
}

.group-title {
	font-size: 14px;
	font-weight: bold;
	color: #24292f;
	margin-bottom: 10px;
	display: block;
}

.button-row {
	display: flex;
	gap: 10px;
	flex-wrap: wrap;
}

.control-btn {
	padding: 8px 16px;
	border: 1px solid #d0d7de;
	border-radius: 6px;
	background-color: #f6f8fa;
	font-size: 14px;
	color: #24292f;
	cursor: pointer;
	transition: all 0.2s;
	flex: 1;
	min-width: 80px;
	text-align: center;
}

.control-btn:hover {
	background-color: #f3f4f6;
	border-color: #8c959f;
}

.control-btn:active {
	background-color: #e5e7ea;
}

.special-btn {
	background-color: #8B0000;
	color: white;
	border-color: #8B0000;
	font-weight: bold;
}

.special-btn:hover {
	background-color: #a52a2a;
	border-color: #a52a2a;
}
</style>
